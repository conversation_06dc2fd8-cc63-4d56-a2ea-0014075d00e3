# Sidebar Navigation Drag Preview Fix

## Issue Description
When dragging sidebar navigation buttons (Dash<PERSON>, Notes, Books, Folders, Timer, Settings), a drag preview appeared showing the button text and the development URL (e.g., "Timer http://localhost:5173/#/timer"). This created a confusing user experience and would also appear in the built version with the actual application URL.

## Root Cause
The sidebar navigation uses `router-link` components, which render as HTML anchor (`<a>`) elements. By default, anchor elements are draggable, and when dragged, browsers show a preview containing:
1. The link text content
2. The `href` attribute (URL)

Since the router uses `createWebHashHistory()`, the URLs are in hash format like `#/timer`, and the full URL gets displayed during drag operations.

## Files Modified
- `src/components/common/SidebarNavigation.vue`

## Solution Implemented
Added `draggable="false"` attribute to all `router-link` elements in the sidebar navigation to disable the default browser drag behavior.

### Before
```vue
<router-link to="/timer" class="nav-button">
    <img src="/icons/timer-icon.svg" class="nav-button__icon" alt="" />
    <span class="nav-button__text">Timer</span>
</router-link>
```

### After
```vue
<router-link to="/timer" class="nav-button" draggable="false">
    <img src="/icons/timer-icon.svg" class="nav-button__icon" alt="" />
    <span class="nav-button__text">Timer</span>
</router-link>
```

## Changes Made
1. **Dashboard button**: Added `draggable="false"`
2. **Notes button**: Added `draggable="false"`
3. **Books button**: Added `draggable="false"`
4. **Folders button**: Added `draggable="false"`
5. **Timer button**: Added `draggable="false"`
6. **Settings button**: Added `draggable="false"`

## Impact
- **User Experience**: Eliminates confusing drag preview when accidentally dragging navigation buttons
- **Built Version**: Prevents URL exposure in production builds
- **Functionality**: Navigation buttons still work normally for clicking
- **Accessibility**: No impact on keyboard navigation or screen readers

## Testing
To verify the fix:
1. Try dragging any sidebar navigation button
2. Confirm no drag preview appears
3. Verify buttons still navigate correctly when clicked
4. Test in both collapsed and expanded sidebar states

## Technical Notes
- The `draggable="false"` attribute is a standard HTML attribute
- This fix works in both development and production builds
- No CSS or JavaScript changes were needed
- The fix is compatible with Vue Router's `router-link` component
