# Sync State Table Integration Plan

## Overview
This plan outlines how to integrate an improved `sync_state` table into the existing sync system to dramatically improve startup performance by enabling fast change detection through hash comparison instead of full database scans.

**Key Principle**: Enhance existing classes rather than creating new ones for seamless integration.

## Current Sync System Architecture

### Key Components
1. **unified-sync-engine.ts** - Main sync orchestrator
2. **change-detector.ts** - Detects changes between local DB and manifest
3. **manifest-manager.ts** - Manages sync-manifest.json files
4. **database-hooks.ts** - Detects database changes for auto-sync
5. **auto-sync.ts** - Handles automatic sync triggers
6. **sync-api.ts** - Public API for sync operations

### Current Flow
```
App Startup → Load Manifest → Compare with DB → Detect Changes → Sync
                                    ↑
                            (Slow: Full DB scan)
```

### Target Flow with sync_state
```
App Startup → Load Manifest → Compare with sync_state → Fast Change Detection → Sync
                                         ↑
                                (Fast: Hash comparison)
```

## Integration Strategy

### ✅ **Enhance Existing Classes**
- Modify existing `ChangeDetector` class
- Enhance existing `DatabaseHooksManager` class
- Update existing `UnifiedSyncEngine` class

### ❌ **Don't Create New Classes**
- No `FastChangeDetector` class
- No `SyncStateUpdater` class
- Keep architecture simple and maintainable

## Phase 1: Database Schema Updates

### 1.1 Update sync_state Table
**File**: `electron/main/database/database.ts`

Add migration to update the existing sync_state table:

```sql
-- Add new columns to existing sync_state table
ALTER TABLE sync_state ADD COLUMN composite_id TEXT;
ALTER TABLE sync_state ADD COLUMN item_name TEXT;
ALTER TABLE sync_state ADD COLUMN sync_path TEXT;
ALTER TABLE sync_state ADD COLUMN last_modified TEXT;

-- Create computed column for composite_id
UPDATE sync_state SET composite_id = item_type || '_' || item_id WHERE composite_id IS NULL;

-- Add new indexes for performance
CREATE INDEX IF NOT EXISTS idx_sync_state_composite_id ON sync_state(composite_id);
CREATE INDEX IF NOT EXISTS idx_sync_state_hash ON sync_state(sync_hash);
CREATE INDEX IF NOT EXISTS idx_sync_state_modified ON sync_state(last_modified);
```

### 1.2 Update TypeScript Interface
**File**: `electron/main/api/sync-logic/types.ts`

Fix the SyncState interface to match actual table:

```typescript
export interface SyncState {
  item_type: 'book' | 'folder' | 'note';
  item_id: number;
  composite_id: string;
  sync_hash: string;
  item_name: string;
  sync_path: string;
  last_modified: string;
  last_synced: string;
  device_id: string;
  sync_version: number;
}
```

## Phase 2: Enhance Database Hooks

### 2.1 Enhance DatabaseHooksManager Class
**File**: `electron/main/database/database-hooks.ts`

Add sync_state population to existing DatabaseHooksManager:

```typescript
export class DatabaseHooksManager extends EventEmitter {
  // Existing properties...

  // Add new method to existing class
  private async updateSyncState(
    type: DatabaseChangeType,
    itemType: DatabaseItemType,
    itemId: number,
    details?: any
  ): Promise<void> {
    if (type === 'delete') {
      // Remove from sync_state
      await dbRun('DELETE FROM sync_state WHERE item_type = ? AND item_id = ?',
                  [itemType, itemId]);
      return;
    }

    // Get current item data
    const item = await this.getCurrentItemData(itemType, itemId);
    if (!item) return;

    // Calculate hash and path
    const hash = this.calculateItemHash(item);
    const name = item.title || item.name || 'Untitled';
    const path = await this.generateItemPath(item);
    const modified = item.updated_at || item.created_at || new Date().toISOString();
    const deviceId = await this.getDeviceId();

    // Update sync_state
    await dbRun(`
      INSERT OR REPLACE INTO sync_state
      (item_type, item_id, composite_id, sync_hash, item_name, sync_path,
       last_modified, last_synced, device_id, sync_version)
      VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, 1)
    `, [itemType, itemId, `${itemType}_${itemId}`, hash, name, path, modified, deviceId]);
  }

  // Add helper methods to existing class
  private async getCurrentItemData(itemType: DatabaseItemType, itemId: number): Promise<any> {
    switch (itemType) {
      case 'book':
        return await dbGet('SELECT * FROM books WHERE id = ?', [itemId]);
      case 'folder':
        return await dbGet('SELECT * FROM folders WHERE id = ?', [itemId]);
      case 'note':
        return await dbGet('SELECT * FROM notes WHERE id = ?', [itemId]);
      default:
        return null;
    }
  }

  private calculateItemHash(item: any): string {
    // Use existing hash calculation from change-detector
    return createHash('sha256').update(JSON.stringify(item)).digest('hex');
  }

  private async generateItemPath(item: any): Promise<string> {
    // Generate path based on item type and relationships
    // This will be implemented based on existing path generation logic
    return `path/to/${item.type}_${item.id}`;
  }

  private async getDeviceId(): Promise<string> {
    // Get device ID from settings or generate one
    return await getSetting('deviceId') || 'default-device';
  }
}
```

### 2.2 Integrate with Existing Change Notifications
Update the existing `notifyChange` method:

```typescript
export class DatabaseHooksManager extends EventEmitter {
  // Existing methods...

  public notifyChange(
    type: DatabaseChangeType,
    itemType: DatabaseItemType,
    itemId: number,
    details?: any,
    options: DatabaseHookOptions = {}
  ): void {
    if (!this.isInitialized) {
      console.warn('[DatabaseHooks] Hooks manager not initialized, skipping change notification');
      return;
    }

    const changeEvent: DatabaseChangeEvent = {
      type,
      itemType,
      itemId,
      timestamp: new Date(),
      details,
      options
    };

    // Add to change history
    this.addToHistory(changeEvent);

    // Update sync_state table (NEW)
    if (!options.skipSyncStateUpdate) {
      this.updateSyncState(type, itemType, itemId, details).catch(error => {
        console.error('[DatabaseHooks] Failed to update sync_state:', error);
      });
    }

    // Emit local event for any listeners (EXISTING)
    this.emit('database-change', changeEvent);

    // Log the change for debugging (EXISTING)
    console.log(`[DatabaseHooks] Change detected: ${type} ${itemType} (ID: ${itemId})`);
  }
}
```

## Phase 3: Enhance Change Detection

### 3.1 Enhance ChangeDetector Class
**File**: `electron/main/api/sync-logic/change-detector.ts`

Add fast comparison method to existing ChangeDetector class:

```typescript
export class ChangeDetector {
  // Existing properties...

  /**
   * Enhanced main comparison method with fast path
   */
  async compareStates(manifest: SyncManifest, syncPath: string): Promise<Changes> {
    // Try fast sync_state comparison first
    try {
      console.log('[ChangeDetector] Using fast sync_state comparison');
      return await this.compareWithSyncState(manifest);
    } catch (error) {
      console.warn('[ChangeDetector] Fast detection failed, falling back to full scan:', error);
      // Fallback to existing method
      return await this.compareWithDatabase(manifest, syncPath);
    }
  }

  /**
   * NEW: Fast manifest comparison using sync_state table
   */
  private async compareWithSyncState(manifest: SyncManifest): Promise<Changes> {
    // Get all sync state in one optimized query
    const syncState = await dbAll<SyncState>(`
      SELECT composite_id, sync_hash, item_name, sync_path, last_modified, item_type
      FROM sync_state
    `);

    const syncMap = new Map(syncState.map(s => [s.composite_id, s]));

    const toImport: ManifestItemsByType = { books: [], folders: [], notes: [] };
    const toExport: LocalItemsByType = { books: [], folders: [], notes: [] };
    const conflicts: ConflictItem[] = [];

    // Check manifest items against sync_state
    for (const manifestItem of manifest.items) {
      const syncItem = syncMap.get(manifestItem.id);

      if (!syncItem) {
        // New item in manifest - import it
        this.categorizeManifestItem(manifestItem, toImport);
      } else if (this.hasConflict(manifestItem, syncItem)) {
        // Hash/name/path mismatch - conflict
        conflicts.push({
          local: this.convertSyncStateToManifestItem(syncItem),
          remote: manifestItem,
          type: 'content'
        });
      }

      // Remove processed items
      syncMap.delete(manifestItem.id);
    }

    // Remaining items in syncMap are local-only (export them)
    for (const [id, syncItem] of syncMap) {
      this.categorizeSyncStateItem(syncItem, toExport);
    }

    return { toImport, toExport, conflicts, toDelete: [] };
  }

  /**
   * RENAMED: Existing comparison method (fallback)
   */
  private async compareWithDatabase(manifest: SyncManifest, syncPath: string): Promise<Changes> {
    // Reset pending deletions for this comparison
    this.pendingDeletions = [];

    // Move existing compareStates implementation here
    const dbBooks = await this.getDbBooks();
    const dbFolders = await this.getDbFolders();
    const dbNotes = await this.getDbNotes();
    const syncHashes = await this.getSyncHashes();

    // ... rest of existing logic ...
    // (Keep all existing comparison logic as fallback)
  }

  // NEW: Helper methods for fast comparison
  private hasConflict(manifestItem: ManifestItem, syncItem: SyncState): boolean {
    return manifestItem.hash !== syncItem.sync_hash ||
           manifestItem.name !== syncItem.item_name ||
           manifestItem.path !== syncItem.sync_path;
  }

  private convertSyncStateToManifestItem(syncItem: SyncState): ManifestItem {
    return {
      id: syncItem.composite_id,
      type: syncItem.item_type,
      name: syncItem.item_name,
      path: syncItem.sync_path,
      hash: syncItem.sync_hash,
      modified: syncItem.last_modified
    };
  }

  private categorizeSyncStateItem(syncItem: SyncState, toExport: LocalItemsByType): void {
    const localItem: LocalItem = {
      id: syncItem.item_id,
      type: syncItem.item_type,
      name: syncItem.item_name,
      content: {}, // Will be populated when needed
      modified: syncItem.last_modified
    };

    switch (syncItem.item_type) {
      case 'book':
        toExport.books.push(localItem);
        break;
      case 'folder':
        toExport.folders.push(localItem);
        break;
      case 'note':
        toExport.notes.push(localItem);
        break;
    }
  }
}
```

## Phase 4: Enhance Sync Engine

### 4.1 Enhance UnifiedSyncEngine Class
**File**: `electron/main/api/sync-logic/unified-sync-engine.ts`

Add sync_state maintenance to existing UnifiedSyncEngine:

```typescript
export class UnifiedSyncEngine extends EventEmitter {
  // Existing properties...

  // Update existing import methods to maintain sync_state
  private async importBook(manifestItem: ManifestItem): Promise<void> {
    // Existing import logic...
    const bookData = await this.parseBookFromManifest(manifestItem);
    const book = await createBook(bookData);

    // NEW: Update sync_state after successful import
    await this.updateSyncStateAfterImport('book', book.id, manifestItem);

    // Existing result tracking...
    this.result.imported.books++;
    this.result.itemsImported++;
  }

  private async importFolder(manifestItem: ManifestItem): Promise<void> {
    // Existing import logic...
    const folderData = await this.parseFolderFromManifest(manifestItem);
    const folder = await createFolder(folderData);

    // NEW: Update sync_state after successful import
    await this.updateSyncStateAfterImport('folder', folder.id, manifestItem);

    // Existing result tracking...
    this.result.imported.folders++;
    this.result.itemsImported++;
  }

  private async importNote(manifestItem: ManifestItem): Promise<void> {
    // Existing import logic...
    const noteData = await this.parseNoteFromManifest(manifestItem);
    const note = await createNote(noteData);

    // NEW: Update sync_state after successful import
    await this.updateSyncStateAfterImport('note', note.id, manifestItem);

    // Existing result tracking...
    this.result.imported.notes++;
    this.result.itemsImported++;
  }

  // NEW: Add sync_state maintenance methods to existing class
  private async updateSyncStateAfterImport(
    itemType: string,
    itemId: number,
    manifestItem: ManifestItem
  ): Promise<void> {
    try {
      await dbRun(`
        INSERT OR REPLACE INTO sync_state
        (item_type, item_id, composite_id, sync_hash, item_name, sync_path,
         last_modified, last_synced, device_id, sync_version)
        VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, 1)
      `, [
        itemType,
        itemId,
        manifestItem.id,
        manifestItem.hash,
        manifestItem.name,
        manifestItem.path,
        manifestItem.modified,
        await this.getDeviceId()
      ]);
    } catch (error) {
      console.error(`[UnifiedSyncEngine] Failed to update sync_state for ${itemType} ${itemId}:`, error);
    }
  }

  private async getDeviceId(): Promise<string> {
    // Use existing device ID logic or get from manifest manager
    return await manifestManager.getDeviceId();
  }
}
```

### 4.2 Add Utility Functions
**File**: `electron/main/api/sync-logic/utils/sync-state-utils.ts`

Create utility functions instead of a separate class:

```typescript
import { dbRun, dbAll, dbGet } from '../../../database/database-api';
import { createHash } from 'crypto';

/**
 * Rebuild sync_state from current database state
 */
export async function rebuildSyncState(): Promise<void> {
  console.log('[SyncStateUtils] Rebuilding sync_state table...');

  try {
    // Clear existing data
    await dbRun('DELETE FROM sync_state');

    // Rebuild from current database
    await rebuildBooksInSyncState();
    await rebuildFoldersInSyncState();
    await rebuildNotesInSyncState();

    console.log('[SyncStateUtils] Sync state rebuilt successfully');
  } catch (error) {
    console.error('[SyncStateUtils] Failed to rebuild sync_state:', error);
    throw error;
  }
}

/**
 * Calculate hash for any item
 */
export function calculateItemHash(item: any): string {
  return createHash('sha256').update(JSON.stringify(item)).digest('hex');
}

/**
 * Generate path for item based on type and relationships
 */
export async function generateItemPath(item: any, itemType: string): Promise<string> {
  // Implementation based on existing path generation logic
  switch (itemType) {
    case 'book':
      return `Books/${sanitizeName(item.title)}/`;
    case 'folder':
      // Get book path and append folder
      const book = await dbGet('SELECT title FROM books WHERE id = ?', [item.book_id]);
      return `Books/${sanitizeName(book?.title || 'Unknown')}/${sanitizeName(item.name)}/`;
    case 'note':
      // Get folder and book paths
      if (item.folder_id) {
        const folder = await dbGet('SELECT name, book_id FROM folders WHERE id = ?', [item.folder_id]);
        const book = await dbGet('SELECT title FROM books WHERE id = ?', [folder?.book_id]);
        return `Books/${sanitizeName(book?.title || 'Unknown')}/${sanitizeName(folder?.name || 'Unknown')}/${sanitizeName(item.title)}.md`;
      } else if (item.book_id) {
        const book = await dbGet('SELECT title FROM books WHERE id = ?', [item.book_id]);
        return `Books/${sanitizeName(book?.title || 'Unknown')}/${sanitizeName(item.title)}.md`;
      }
      return `Notes/${sanitizeName(item.title)}.md`;
    default:
      return `Unknown/${itemType}_${item.id}`;
  }
}

// Helper functions
async function rebuildBooksInSyncState(): Promise<void> {
  const books = await dbAll('SELECT * FROM books');
  for (const book of books) {
    await insertSyncStateItem('book', book);
  }
}

async function rebuildFoldersInSyncState(): Promise<void> {
  const folders = await dbAll('SELECT * FROM folders');
  for (const folder of folders) {
    await insertSyncStateItem('folder', folder);
  }
}

async function rebuildNotesInSyncState(): Promise<void> {
  const notes = await dbAll('SELECT * FROM notes');
  for (const note of notes) {
    await insertSyncStateItem('note', note);
  }
}

async function insertSyncStateItem(itemType: string, item: any): Promise<void> {
  const hash = calculateItemHash(item);
  const name = item.title || item.name || 'Untitled';
  const path = await generateItemPath(item, itemType);
  const modified = item.updated_at || item.created_at || new Date().toISOString();

  await dbRun(`
    INSERT OR REPLACE INTO sync_state
    (item_type, item_id, composite_id, sync_hash, item_name, sync_path,
     last_modified, last_synced, device_id, sync_version)
    VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, 'rebuild', 1)
  `, [itemType, item.id, `${itemType}_${item.id}`, hash, name, path, modified]);
}

function sanitizeName(name: string): string {
  return name.replace(/[<>:"/\\|?*]/g, '_').trim();
}
```

## Phase 5: Performance Optimizations

### 5.1 Batch Operations
Optimize database operations for better performance:

```typescript
// In sync-state-updater.ts
async batchUpdateSyncState(updates: SyncStateUpdate[]): Promise<void> {
  await withTransaction(async () => {
    for (const update of updates) {
      await this.updateAfterSync(update.itemType, update.itemId, update.manifestItem);
    }
  });
}
```

### 5.2 Enhance SyncAPI for Startup Optimization
**File**: `electron/main/api/sync-logic/sync-api.ts`

Add startup sync state validation to existing SyncAPI:

```typescript
import { rebuildSyncState } from './utils/sync-state-utils';

export class SyncAPI extends EventEmitter {
  // Existing properties...

  // Enhance existing initialization
  async initializeSync(): Promise<void> {
    // NEW: Check if sync_state is populated
    const syncStateCount = await dbGet<{count: number}>('SELECT COUNT(*) as count FROM sync_state');

    if (syncStateCount.count === 0) {
      console.log('[SyncAPI] sync_state table empty, rebuilding...');
      await rebuildSyncState();
    }

    // Existing initialization logic...
    // (Keep all existing initialization code)
  }

  // NEW: Add method to manually rebuild sync state
  async rebuildSyncState(): Promise<void> {
    try {
      await rebuildSyncState();
      this.emit('sync-state:rebuilt');
    } catch (error) {
      console.error('[SyncAPI] Failed to rebuild sync state:', error);
      throw error;
    }
  }
}
```

## Phase 6: Migration Strategy

### 6.1 Database Migration
**File**: `electron/main/database/database.ts`

Add migration in `handleDatabaseMigrations`:

```typescript
// Add sync_state table improvements
try {
  // Check if new columns exist
  const tableInfo = await allAsync(db, "PRAGMA table_info(sync_state)");
  const hasCompositeId = tableInfo.some(col => col.name === 'composite_id');

  if (!hasCompositeId) {
    console.log('Migrating sync_state table...');

    // Add new columns
    await runAsync(db, 'ALTER TABLE sync_state ADD COLUMN composite_id TEXT');
    await runAsync(db, 'ALTER TABLE sync_state ADD COLUMN item_name TEXT');
    await runAsync(db, 'ALTER TABLE sync_state ADD COLUMN sync_path TEXT');
    await runAsync(db, 'ALTER TABLE sync_state ADD COLUMN last_modified TEXT');

    // Populate composite_id for existing records
    await runAsync(db, `
      UPDATE sync_state
      SET composite_id = item_type || '_' || item_id
      WHERE composite_id IS NULL
    `);

    console.log('sync_state table migration completed');
  }
} catch (error) {
  console.error('Error migrating sync_state table:', error);
}
```

### 6.2 Gradual Rollout
1. **Phase 6.1**: Deploy schema changes
2. **Phase 6.2**: Enable sync_state population via hooks
3. **Phase 6.3**: Enable fast change detection (with fallback)
4. **Phase 6.4**: Remove legacy change detection after validation

## Phase 7: Testing & Validation

### 7.1 Performance Testing
Create benchmarks to measure improvement:

```typescript
// In tests/sync-performance.test.ts
describe('Sync Performance', () => {
  it('should be faster with sync_state table', async () => {
    // Create test data (1000 items)
    await createTestData(1000);

    // Measure legacy detection
    const legacyStart = Date.now();
    await changeDetector.compareStatesLegacy(manifest, syncPath);
    const legacyTime = Date.now() - legacyStart;

    // Measure fast detection
    const fastStart = Date.now();
    await fastChangeDetector.compareWithSyncState(manifest);
    const fastTime = Date.now() - fastStart;

    expect(fastTime).toBeLessThan(legacyTime * 0.1); // Should be 10x faster
  });
});
```

### 7.2 Data Integrity Testing
Ensure sync_state stays in sync with actual data:

```typescript
describe('Sync State Integrity', () => {
  it('should maintain sync_state accuracy', async () => {
    // Create/update/delete items
    const book = await createBook(testBookData);
    await updateBook(book.id, updatedData);
    await deleteBook(book.id);

    // Verify sync_state reflects changes
    const syncState = await dbAll('SELECT * FROM sync_state WHERE item_type = "book"');
    expect(syncState).toHaveLength(0); // Should be deleted
  });
});
```

## Files to Modify (Not Create)

### Core Files
1. **`electron/main/database/database.ts`** - Schema updates and migrations
2. **`electron/main/api/sync-logic/types.ts`** - Interface fixes
3. **`electron/main/database/database-hooks.ts`** - Add sync_state population
4. **`electron/main/api/sync-logic/change-detector.ts`** - Add fast comparison method
5. **`electron/main/api/sync-logic/unified-sync-engine.ts`** - Add sync_state maintenance
6. **`electron/main/api/sync-logic/sync-api.ts`** - Add startup validation

### New Utility File (Optional)
7. **`electron/main/api/sync-logic/utils/sync-state-utils.ts`** - Utility functions

## Expected Performance Improvements

### Before (Current System)
- **Startup sync check**: 2-5 seconds for 1000 items
- **Database queries**: 3 separate queries (books, folders, notes)
- **Memory usage**: High (loads all items into memory)
- **Scalability**: Degrades with library size

### After (With sync_state)
- **Startup sync check**: 100-200ms for 1000 items
- **Database queries**: 1 optimized query
- **Memory usage**: Low (only hash comparison)
- **Scalability**: Consistent performance regardless of size

## Implementation Timeline

- **Week 1**: Phase 1-2 (Schema updates, enhance database hooks)
- **Week 2**: Phase 3-4 (Enhance change detection, enhance sync engine)
- **Week 3**: Phase 5-6 (Performance optimizations, migration strategy)
- **Week 4**: Phase 7 (Testing, validation, deployment)

## Benefits of This Simplified Approach

1. **Minimal Code Changes**: Work with existing architecture
2. **No Breaking Changes**: Existing APIs remain the same
3. **Easier Testing**: Test existing classes with new functionality
4. **Simpler Maintenance**: Fewer files to manage
5. **Natural Integration**: Fits existing patterns
6. **Gradual Rollout**: Can enable features incrementally

## Risk Mitigation

1. **Fallback Strategy**: Keep legacy change detection as backup
2. **Data Validation**: Regular sync_state integrity checks
3. **Gradual Rollout**: Enable features incrementally
4. **Monitoring**: Track performance metrics and error rates
5. **Existing Architecture**: Build on proven, stable foundation

This simplified plan provides a comprehensive approach to integrating the improved sync_state table while maintaining system reliability and achieving significant performance gains through enhancement of existing classes rather than creating new ones.
```
