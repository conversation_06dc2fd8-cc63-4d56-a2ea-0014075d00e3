# Sync State Table Analysis

## Files Modified
- Analysis of sync_state table usage across the sync system

## What Was Done
Comprehensive analysis of how the sync_state table is programmed and used throughout the codebase.

## Database Schema

### sync_state Table Structure
```sql
CREATE TABLE IF NOT EXISTS sync_state (
    item_type TEXT NOT NULL,
    item_id INTEGER NOT NULL,
    sync_hash TEXT NOT NULL,
    last_synced TIMESTAMP NOT NULL,
    device_id TEXT NOT NULL,
    sync_version INTEGER DEFAULT 1,
    PRIMARY KEY (item_type, item_id)
)
```

**Key Characteristics:**
- **Composite Primary Key**: `(item_type, item_id)` - No single `id` column
- **item_type**: Values are 'book', 'folder', or 'note'
- **item_id**: References the ID in the respective table (books.id, folders.id, notes.id)
- **sync_hash**: Content hash for change detection
- **last_synced**: Timestamp of last sync operation
- **device_id**: Which device last synced this item
- **sync_version**: For future sync protocol changes

### Related Tables

#### sync_sessions Table
```sql
CREATE TABLE IF NOT EXISTS sync_sessions (
    id TEXT PRIMARY KEY,
    device_id TEXT NOT NULL,
    started_at TIMESTAMP NOT NULL,
    completed_at TIMESTAMP,
    status TEXT NOT NULL CHECK (status IN ('in_progress', 'completed', 'failed')),
    items_synced INTEGER DEFAULT 0,
    error_message TEXT
)
```

#### sync_directory_state Table
```sql
CREATE TABLE IF NOT EXISTS sync_directory_state (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    directory TEXT UNIQUE NOT NULL,
    last_sync_hash TEXT,
    last_sync_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
)
```

## Current Usage in Codebase

### 1. Database Creation
**File**: `electron/main/database/database.ts`
- Lines 237-245: Creates the sync_state table
- Line 359: Creates index `idx_sync_state_last_synced`

### 2. Change Detection
**File**: `electron/main/api/sync-logic/change-detector.ts`
- Lines 343-362: `getSyncHashes()` method reads from sync_state
- **Critical Fix Applied**: Query now correctly handles composite key:
```typescript
const query = `
  SELECT 
    item_type || '_' || item_id as composite_id,
    sync_hash as last_hash
  FROM sync_state
`;
```

### 3. TypeScript Interface
**File**: `electron/main/api/sync-logic/types.ts`
- Lines 237-254: Defines `SyncState` interface
- **Note**: Interface includes `id` field but actual table uses composite key

### 4. Database Scripts
**File**: `scripts/dumpDatabaseContents.ts` and `scripts/dumpDatabaseContents.cjs`
- Special handling for sync_state table due to composite key
- Uses `ORDER BY item_type ASC, item_id ASC` instead of `ORDER BY id ASC`

## Current Status and Issues

### ✅ What's Working
1. **Table Creation**: sync_state table is properly created in database
2. **Index Creation**: Performance index on last_synced column
3. **Change Detection**: Fixed query correctly reads sync hashes
4. **Composite Key Handling**: Scripts properly handle the composite primary key

### ⚠️ Current Limitations
1. **Limited Usage**: Table is created but minimally used
2. **No Write Operations**: No code currently inserts/updates sync_state records
3. **Interface Mismatch**: TypeScript interface doesn't match actual table structure
4. **Commented Out Code**: Several references to sync_state operations are commented out

### 🚨 Key Findings

#### 1. Underutilized Resource
The sync_state table exists and is properly structured but is barely used:
- Only read operations in change-detector.ts
- No insert/update operations found
- Sync system primarily uses manifest files instead

#### 2. Schema Evolution
The codebase shows evidence of sync system evolution:
- **sync_state**: Item-based tracking (original design)
- **sync_directory_state**: Directory-based tracking (newer approach)
- **Manifest files**: File-based state tracking (current primary method)

#### 3. Commented Out Operations
Several files contain commented-out sync_state operations:
```typescript
// sync-api.ts line 472
// await dbRun('DELETE FROM sync_state WHERE sync_directory = ?', [directory]);

// sync-api.ts lines 453-455
// For now, return empty array as we don't have sync_state table yet
// This will be implemented when database schema is updated
```

## Relationship to Sync System

### Current Architecture
The sync system uses a **hybrid approach**:

1. **Primary**: Manifest files (`sync-manifest.json`) for state tracking
2. **Secondary**: `sync_directory_state` table for directory-level tracking
3. **Unused**: `sync_state` table for item-level tracking

### Data Flow
```
Database Changes → Change Detector → Manifest Generation → File Sync
                      ↓
                 sync_state table (read-only for hash comparison)
```

### Integration Points
1. **Change Detection**: Reads sync_state to compare current vs last sync hashes
2. **Database Hooks**: Could potentially write to sync_state (not implemented)
3. **Sync History**: Could use sync_state for audit trail (not implemented)

## Detailed File Usage Analysis

### electron/main/database/database.ts
**Lines 237-245**: Table creation
```sql
CREATE TABLE IF NOT EXISTS sync_state (
    item_type TEXT NOT NULL,
    item_id INTEGER NOT NULL,
    sync_hash TEXT NOT NULL,
    last_synced TIMESTAMP NOT NULL,
    device_id TEXT NOT NULL,
    sync_version INTEGER DEFAULT 1,
    PRIMARY KEY (item_type, item_id)
)
```

**Line 359**: Index creation
```sql
CREATE INDEX IF NOT EXISTS idx_sync_state_last_synced ON sync_state(last_synced)
```

### electron/main/api/sync-logic/change-detector.ts
**Lines 343-362**: Only active usage of sync_state table
```typescript
private async getSyncHashes(): Promise<Map<string, string>> {
  try {
    const query = `
      SELECT
        item_type || '_' || item_id as composite_id,
        sync_hash as last_hash
      FROM sync_state
    `;

    const syncItems = await dbAll<any>(query);
    return new Map(syncItems.map(item => [
      item.composite_id,
      item.last_hash
    ]));
  } catch (error) {
    // If table doesn't exist yet, return empty map
    console.log('sync_state table not found, returning empty hash map');
    return new Map();
  }
}
```

### electron/main/api/sync-logic/unified-sync-engine.ts
**Lines 987-1011**: Uses sync_directory_state instead of sync_state
```typescript
private async updateSyncState(directory: string, hash: string): Promise<void> {
  await withTransaction(async () => {
    // Create sync_directory_state table if it doesn't exist
    await dbRun(`
      CREATE TABLE IF NOT EXISTS sync_directory_state (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        directory TEXT UNIQUE NOT NULL,
        last_sync_hash TEXT,
        last_sync_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Update or insert sync state
    await dbRun(`
      INSERT INTO sync_directory_state (directory, last_sync_hash, updated_at)
      VALUES (?, ?, CURRENT_TIMESTAMP)
      ON CONFLICT(directory) DO UPDATE SET
        last_sync_hash = excluded.last_sync_hash,
        last_sync_at = CURRENT_TIMESTAMP,
        updated_at = CURRENT_TIMESTAMP
    `, [directory, hash]);
  });
}
```

### electron/main/api/sync-logic/sync-api.ts
**Lines 465-487**: Commented out sync_state operations
```typescript
async clearSyncState(directory: string): Promise<void> {
  try {
    // Clear manifest
    const defaultManifest = await manifestManager.createDefaultManifest();
    await manifestManager.saveManifest(directory, defaultManifest);

    // Clear sync history from database (when implemented)
    // await dbRun('DELETE FROM sync_state WHERE sync_directory = ?', [directory]);

    // Reset status
    if (this.currentStatus.syncDirectory === directory) {
      this.currentStatus.lastSync = null;
      this.currentStatus.lastResult = null;
      this.currentStatus.errors = [];
    }

    this.emit('state:cleared', directory);

  } catch (error) {
    console.error('Error clearing sync state:', error);
    throw error;
  }
}
```

**Lines 451-460**: Placeholder for sync history
```typescript
async getSyncHistory(directory?: string): Promise<SyncHistoryEntry[]> {
  try {
    // For now, return empty array as we don't have sync_state table yet
    // This will be implemented when database schema is updated
    return [];
  } catch (error) {
    console.error('Error getting sync history:', error);
    throw error;
  }
}
```

### electron/main/api/sync-logic/types.ts
**Lines 237-254**: Interface definition (mismatched with actual table)
```typescript
export interface SyncState {
  /** Database ID */
  id: number;  // ❌ Table doesn't have this field
  /** Type of item being tracked */
  item_type: 'book' | 'folder' | 'note';
  /** ID of the item in its respective table */
  item_id: number;
  /** Content hash for change detection */
  sync_hash: string;
  /** Last sync timestamp */
  last_synced: string;
  /** Sync status */
  sync_status: 'synced' | 'pending' | 'conflict';  // ❌ Table doesn't have this field
  /** Created timestamp */
  created_at: string;  // ❌ Table doesn't have this field
  /** Updated timestamp */
  updated_at: string;  // ❌ Table doesn't have this field
}
```

## Missing Operations

### No Insert/Update Operations Found
Despite the table being created, there are **no active insert or update operations** in the codebase:

1. **No sync_state inserts** when items are created/modified
2. **No sync_state updates** when items are synced
3. **No sync_state deletes** when items are removed
4. **No database hooks** to automatically populate sync_state

### Commented Out Operations
Several files show evidence of planned but unimplemented sync_state operations:

**sync-api.ts**:
- Line 472: `// await dbRun('DELETE FROM sync_state WHERE sync_directory = ?', [directory]);`
- Lines 453-455: Comments about implementing sync_state table usage

## Recommendations

### Immediate Actions
1. **Fix TypeScript Interface**: Update `SyncState` interface to match actual table schema
2. **Implement Write Operations**: Add functions to insert/update sync_state records
3. **Add Database Hooks**: Auto-populate sync_state when items change
4. **Uncomment Operations**: Implement the commented-out sync_state operations

### Architecture Decisions Needed
1. **Choose Primary Sync State Method**: Database tables vs manifest files
2. **Unify Sync Tables**: Decide between sync_state vs sync_directory_state
3. **Define Sync Strategy**: Item-level vs directory-level tracking

## Technical Debt Summary
- **Unused Infrastructure**: sync_state table exists but is barely used
- **Schema Mismatches**: TypeScript interfaces don't match database schema
- **Incomplete Implementation**: Many sync_state operations are commented out
- **Multiple State Systems**: Database tables, manifest files, and directory state all coexist
- **No Write Path**: Only read operations exist for sync_state table
