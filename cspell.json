{"version": "0.2", "language": "en", "words": ["noti", "keybind", "keybinds", "Keybinds", "Uncategorized", "dompurify", "AUTOINCREMENT", "pomodoro", "Pomodoro", "tiptap", "Tiptap", "rollup", "Rollup", "vite", "Vite", "vue", "<PERSON><PERSON>", "typescript", "TypeScript", "eslint", "ESLint", "sqlite", "SQLite", "sqlite3", "electron", "Electron", "nodejs", "Node.js", "npm", "NPM", "pnpm", "PNPM", "yarn", "Yarn", "webpack", "Webpack", "babel", "<PERSON>l", "postcss", "PostCSS", "autoprefixer", "Autoprefixer", "tailwind", "Tailwind", "css", "CSS", "scss", "SCSS", "sass", "Sass", "html", "HTML", "json", "JSON", "yaml", "YAML", "toml", "TOML", "markdown", "<PERSON><PERSON>", "md", "MD", "jsx", "JSX", "tsx", "TSX", "api", "API", "ipc", "IPC", "url", "URL", "uri", "URI", "uuid", "UUID", "isbn", "ISBN", "olid", "OLID", "openlibrary", "OpenLibrary", "google", "Google", "github", "GitHub", "discord", "Discord", "rpc", "RPC", "o<PERSON>h", "OAuth", "jwt", "JWT", "cors", "CORS", "csrf", "CSRF", "xss", "XSS", "sql", "SQL", "nosql", "NoSQL", "crud", "CRUD", "rest", "REST", "graphql", "GraphQL", "websocket", "WebSocket", "http", "HTTP", "https", "HTTPS", "tcp", "TCP", "udp", "UDP", "ip", "IP", "dns", "DNS", "cdn", "CDN", "ssl", "SSL", "tls", "TLS", "utf", "UTF", "ascii", "ASCII", "unicode", "Unicode", "regex", "RegEx", "regexp", "RegExp", "dom", "DOM", "bom", "BOM", "xhr", "XHR", "ajax", "AJAX", "fetch", "<PERSON>tch", "async", "Async", "await", "Await", "promise", "Promise", "callback", "Callback", "eventloop", "EventLoop", "microtask", "Microtask", "macrotask", "Macrotask", "debounce", "<PERSON><PERSON><PERSON><PERSON>", "throttle", "<PERSON>hrottle", "memoize", "Memoize", "immutable", "Immutable", "mutable", "Mutable", "readonly", "Read<PERSON>nly", "nullable", "Nullable", "undefined", "Undefined", "boolean", "Boolean", "string", "String", "number", "Number", "object", "Object", "array", "Array", "function", "Function", "class", "Class", "interface", "Interface", "enum", "Enum", "type", "Type", "generic", "Generic", "tuple", "<PERSON><PERSON>", "union", "Union", "intersection", "Intersection", "literal", "Literal", "template", "Template", "namespace", "Namespace", "module", "<PERSON><PERSON><PERSON>", "import", "Import", "export", "Export", "default", "<PERSON><PERSON><PERSON>", "const", "Const", "let", "Let", "var", "Var", "if", "If", "else", "Else", "switch", "Switch", "case", "Case", "break", "Break", "continue", "Continue", "for", "For", "while", "While", "do", "Do", "try", "Try", "catch", "Catch", "finally", "Finally", "throw", "<PERSON>hrow", "return", "Return", "yield", "Yield", "new", "New", "this", "This", "super", "Super", "static", "Static", "public", "Public", "private", "Private", "protected", "Protected", "abstract", "Abstract", "extends", "Extends", "implements", "Implements", "instanceof", "InstanceOf", "typeof", "TypeOf", "keyof", "KeyOf", "in", "In", "of", "Of", "with", "With", "delete", "Delete", "void", "Void", "null", "<PERSON><PERSON>", "true", "True", "false", "False"], "ignorePaths": ["node_modules/**", "dist/**", "build/**", ".git/**", "*.min.js", "*.min.css", "package-lock.json", "yarn.lock", "pnpm-lock.yaml"], "ignoreRegExpList": ["/\\b[A-Z]{2,}\\b/g", "/\\b\\d+\\b/g", "/\\b[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}\\b/gi", "/\\bhttps?:\\/\\/[^\\s]+/gi", "/\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b/gi"], "overrides": [{"filename": "**/*.vue", "words": ["teleport", "Teleport", "composables", "Composables", "reactivity", "Reactivity", "ref", "Ref", "reactive", "Reactive", "computed", "Computed", "watch", "Watch", "watchEffect", "WatchEffect", "onMounted", "OnMounted", "onUnmounted", "OnUnmounted", "onUpdated", "OnUpdated", "onBeforeMount", "OnBeforeMount", "onBeforeUnmount", "OnBeforeUnmount", "onBeforeUpdate", "OnBeforeUpdate", "nextTick", "NextTick", "defineComponent", "DefineComponent", "defineProps", "DefineProps", "defineEmits", "DefineEmits", "defineExpose", "DefineExpose", "with<PERSON><PERSON><PERSON><PERSON>", "With<PERSON><PERSON>ault<PERSON>", "toRefs", "ToRefs", "toRef", "ToRef", "unref", "Unref", "isRef", "IsRef", "shallowRef", "ShallowRef", "triggerRef", "TriggerRef", "customRef", "CustomRef", "readonly", "Read<PERSON>nly", "shallowReadonly", "ShallowR<PERSON><PERSON>ly", "is<PERSON><PERSON><PERSON>ly", "Is<PERSON><PERSON>only", "isProxy", "IsProxy", "isReactive", "IsReactive", "toRaw", "ToRaw", "mark<PERSON>aw", "Mark<PERSON><PERSON>", "effectScope", "EffectScope", "getCurrentScope", "GetCurrentScope", "onScopeDispose", "OnScopeDispose"]}, {"filename": "**/*.ts", "words": ["typeof", "keyof", "readonly", "partial", "required", "record", "pick", "omit", "exclude", "extract", "nonnullable", "returntype", "instancetype", "thistype", "parameters", "constructorparameters", "awaited", "uppercase", "lowercase", "capitalize", "uncapitalize"]}]}