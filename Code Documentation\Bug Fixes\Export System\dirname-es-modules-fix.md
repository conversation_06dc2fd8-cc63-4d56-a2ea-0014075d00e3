# __dirname ES Modules Fix

## Issue Description
User reported getting `ReferenceError: __dirname is not defined` error when trying to export notes in .noti format. The error occurred in the `exportNoteToNotiToPath` function when trying to read the package.json file to get the app version.

## Root Cause
The Electron application is using ES modules, and `__dirname` is not available in ES modules. It's only available in CommonJS modules.

## Error Details
```
Error exporting to Noti format: ReferenceError: __dirname is not defined
    at exportNoteToNotiToPath (file:///C:/Users/<USER>/Desktop/Noti/Noti/dist-electron/main/index.js:2946:50)
```

The error occurred at these lines in `notes-api.ts`:
- Line 828: `const packageJsonPath = path.join(__dirname, '../../../package.json');`
- Line 939: `const packageJsonPath = path.join(__dirname, '../../../package.json');`

## Solution Implemented

### 1. Added ES Modules Import
Added the necessary import for `fileURLToPath` from the `url` module:

```typescript
import { fileURLToPath } from 'url';
```

### 2. Created __dirname Equivalent
Added ES modules equivalent of `__dirname`:

```typescript
// ES modules equivalent of __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
```

### How It Works
- `import.meta.url` provides the URL of the current module
- `fileURLToPath()` converts the file URL to a file path
- `path.dirname()` gets the directory name from the file path
- This recreates the same functionality as the CommonJS `__dirname`

## Files Modified
- **electron/main/api/notes-api.ts**: Added ES modules __dirname equivalent

## Testing
The fix should resolve the .noti export functionality. The existing code that uses `__dirname` will now work correctly in the ES modules environment.

## References
- [Node.js ES Modules Documentation](https://nodejs.org/api/esm.html)
- [MDN import.meta Documentation](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Operators/import.meta)
- [Stack Overflow: Alternative for __dirname in ES6 modules](https://stackoverflow.com/questions/46745014/alternative-for-dirname-in-node-js-when-using-es6-modules)
