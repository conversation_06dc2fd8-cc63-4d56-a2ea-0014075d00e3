/**
 * Ultra-early component preloading
 * 
 * This script runs before the main app initialization to preload components
 * as early as possible in the startup process
 */

console.log('⚡ Ultra-early preloading started...')
const ultraEarlyStart = performance.now()

// Start preloading the heaviest components immediately
const preloadPromises = [
  // Preload in order of complexity (heaviest first)
  import('./views/NotesView.vue').then(() => {
    console.log('⚡ Notes component ultra-preloaded')
  }),
  import('./views/FoldersView.vue').then(() => {
    console.log('⚡ Folders component ultra-preloaded')
  }),
  import('./views/BooksView.vue').then(() => {
    console.log('⚡ Books component ultra-preloaded')
  }),
  import('./views/TimerView.vue').then(() => {
    console.log('⚡ Timer component ultra-preloaded')
  }),
  import('./views/SettingsView.vue').then(() => {
    console.log('⚡ Settings component ultra-preloaded')
  })
]

// Also preload some heavy dependencies that are commonly used
const dependencyPromises = [
  import('./components/notes/NoteEditor.vue').then(() => {
    console.log('⚡ NoteEditor component ultra-preloaded')
  }),
  import('./components/folders/FolderNavigator.vue').then(() => {
    console.log('⚡ FolderNavigator component ultra-preloaded')
  }),
  import('./components/books/BookCard.vue').then(() => {
    console.log('⚡ BookCard component ultra-preloaded')
  }),
  import('./components/timer/PomodoroTimer.vue').then(() => {
    console.log('⚡ PomodoroTimer component ultra-preloaded')
  })
]

// Track completion
Promise.all([...preloadPromises, ...dependencyPromises]).then(() => {
  const totalTime = performance.now() - ultraEarlyStart
  console.log(`⚡ Ultra-early preloading completed in ${totalTime.toFixed(2)}ms`)
  
  // Store preload completion time for analysis
  ;(window as any).preloadStats = {
    ultraEarlyTime: totalTime,
    completedAt: Date.now()
  }
}).catch(error => {
  console.error('❌ Ultra-early preloading failed:', error)
})

export { preloadPromises, dependencyPromises }
