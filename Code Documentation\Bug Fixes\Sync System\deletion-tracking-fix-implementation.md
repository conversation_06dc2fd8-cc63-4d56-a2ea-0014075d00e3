# Deletion Tracking Fix Implementation

## Files Modified
- `electron/main/api/sync-logic/unified-sync-engine.ts`
- `electron/main/database/database-hooks.ts`

## What Was Done
Fixed the critical gap in deletion tracking where local deletions were not being recorded in the sync manifest, preventing deletions from propagating to other devices.

## How It Was Fixed/Implemented

### Root Cause
The sync system had complete deletion tracking infrastructure but a critical gap: when items were deleted locally, the sync engine would regenerate the manifest from the database (which only contains existing items) instead of recording the deletions in `manifest.deletions[]`.

**Current Broken Flow:**
1. User deletes item in Noti app ✅
2. Database deletion occurs ✅  
3. Database hooks trigger (`notifyNoteChange('delete', ...)`) ✅
4. Auto-sync runs and calls `unified-sync-engine.sync()` ✅
5. **PROBLEM**: Sync engine calls `generateManifestFromDatabase()` which only includes existing items ❌
6. Manifest is saved without deletion records ❌
7. Other devices sync but never see the deletion ❌

### Solution Architecture
The fix involves modifying the database hooks system to track pending deletions and the sync engine to record these deletions in the manifest before regenerating it from the database.

**Fixed Flow:**
1. User deletes item in Noti app ✅
2. Database deletion occurs ✅
3. Database hooks trigger and **store deletion info** ✅
4. Auto-sync runs and calls `unified-sync-engine.sync()` ✅
5. **NEW**: Sync engine retrieves pending deletions from database hooks ✅
6. **NEW**: Sync engine records deletions in manifest using `manifestManager.removeItem()` ✅
7. Sync engine generates fresh manifest from database (existing items) ✅
8. Manifest is saved with both existing items AND deletion records ✅
9. Other devices sync and process the deletion records ✅

### Implementation Details

#### 1. Enhanced Database Hooks (`database-hooks.ts`)
Added pending deletion tracking to store deletion information until the next sync operation:

```typescript
// Track pending deletions until next sync
private pendingDeletions: Array<{
  id: string;
  type: 'book' | 'folder' | 'note';
  deletedAt: string;
  details?: any;
}> = [];

// Store deletion info when items are deleted
private storeDeletionInfo(changeEvent: DatabaseChangeEvent): void {
  if (changeEvent.type === 'delete') {
    const itemId = `${changeEvent.itemType}_${changeEvent.itemId}`;

    // Check if this deletion is already tracked
    const existingIndex = this.pendingDeletions.findIndex(d => d.id === itemId);

    if (existingIndex === -1) {
      this.pendingDeletions.push({
        id: itemId,
        type: changeEvent.itemType as 'book' | 'folder' | 'note',
        deletedAt: changeEvent.timestamp.toISOString(),
        details: changeEvent.details
      });
      console.log(`[DatabaseHooks] Stored deletion info for ${itemId}`);
    }
  }
}

// Allow sync system to retrieve and clear pending deletions
public getPendingDeletions(): Array<{id: string; type: string; deletedAt: string; details?: any}> {
  return [...this.pendingDeletions];
}

public clearPendingDeletions(): void {
  const count = this.pendingDeletions.length;
  this.pendingDeletions = [];
  if (count > 0) {
    console.log(`[DatabaseHooks] Cleared ${count} pending deletions`);
  }
}
```

#### 2. Sync Engine Modifications (`unified-sync-engine.ts`)
Added deletion recording logic before manifest regeneration:

```typescript
// Record pending deletions in manifest before regenerating from database
private async recordPendingDeletions(directory: string): Promise<void> {
  const pendingDeletions = databaseHooks.getPendingDeletions();

  if (pendingDeletions.length > 0) {
    console.log(`[UnifiedSyncEngine] Recording ${pendingDeletions.length} pending deletions in manifest`);

    try {
      // Load current manifest to record deletions
      const currentManifest = await manifestManager.loadManifest(directory);

      // Record each deletion in the manifest
      for (const deletion of pendingDeletions) {
        manifestManager.removeItem(currentManifest, deletion.id);
        console.log(`[UnifiedSyncEngine] Recorded deletion of ${deletion.type} ${deletion.id} in manifest`);
      }

      // Save manifest with deletion records
      await manifestManager.saveManifest(directory, currentManifest);

      // Clear pending deletions after recording
      databaseHooks.clearPendingDeletions();

      console.log(`[UnifiedSyncEngine] Successfully recorded ${pendingDeletions.length} deletions in manifest`);
    } catch (error) {
      console.error('[UnifiedSyncEngine] Error recording pending deletions:', error);
      // Don't clear pending deletions if recording failed - they'll be retried next sync
    }
  }
}

// Modified sync() method to record deletions before manifest regeneration
async sync(directory: string): Promise<SyncResult> {
  // ... existing sync logic ...

  // NEW: Record pending deletions before updating manifest
  await this.recordPendingDeletions(directory);

  // Generate complete manifest from database state (existing items only)
  const populatedManifest = await manifestManager.generateManifestFromDatabase();
  await manifestManager.saveManifest(directory, populatedManifest);

  // ... rest of sync logic ...
}
```

### Key Technical Details

**Deletion ID Format**: Uses the same format as the sync system (`book_123`, `folder_456`, `note_789`) for consistency.

**Timing**: Deletions are recorded immediately before the manifest is regenerated from the database, ensuring deletion records are preserved.

**Cleanup**: Pending deletions are cleared after being recorded to prevent duplicate entries.

**Error Handling**: Includes proper error handling and logging for debugging deletion tracking issues.

#### 3. Physical File Cleanup (`unified-sync-engine.ts`)
Added physical file system cleanup when recording deletions:

```typescript
// Delete physical files/folders from sync directory when items are deleted
private async deletePhysicalFiles(directory: string, manifestItem: any): Promise<void> {
  try {
    const itemPath = path.join(directory, manifestItem.path);

    if (await fileOperations.exists(itemPath)) {
      if (manifestItem.type === 'note') {
        // Delete note file
        await fs.unlink(itemPath);
        console.log(`[UnifiedSyncEngine] Deleted note file: ${itemPath}`);
      } else if (manifestItem.type === 'folder' || manifestItem.type === 'book') {
        // Delete directory and all contents
        await fs.rm(itemPath, { recursive: true, force: true });
        console.log(`[UnifiedSyncEngine] Deleted ${manifestItem.type} directory: ${itemPath}`);
      }

      // Clean up empty parent directories
      await this.cleanupEmptyParentDirectories(path.dirname(itemPath));
    } else {
      console.log(`[UnifiedSyncEngine] Physical file/folder not found for deletion: ${itemPath}`);
    }
  } catch (error) {
    console.error(`[UnifiedSyncEngine] Error deleting physical files for ${manifestItem.id}:`, error);
    // Continue with manifest recording even if physical deletion fails
  }
}

// Enhanced recordPendingDeletions to include physical cleanup
private async recordPendingDeletions(directory: string): Promise<void> {
  // ... existing code ...

  for (const deletion of pendingDeletions) {
    // Find the item in manifest to get its path before removing it
    const manifestItem = manifestManager.findItem(currentManifest, deletion.id);

    if (manifestItem) {
      // Delete physical files/folders from sync directory
      await this.deletePhysicalFiles(directory, manifestItem);
    }

    // Record deletion in manifest
    manifestManager.removeItem(currentManifest, deletion.id);
  }
}
```

### Impact
- ✅ **Fixes Core Issue**: Local deletions now propagate to other devices
- ✅ **Physical File Cleanup**: Files and folders are deleted from sync directory
- ✅ **No Breaking Changes**: Maintains full backward compatibility
- ✅ **Preserves Infrastructure**: Uses existing `manifestManager.removeItem()` method
- ✅ **Minimal Code Changes**: Focused fix with minimal surface area
- ✅ **Proper Cleanup**: Prevents accumulation of stale deletion records and orphaned files

### Testing Recommendations

#### Database & Sync Propagation Tests:
1. **Basic Deletion Test**: Delete a note on Device A, sync, verify it disappears on Device B
2. **Multi-Item Test**: Delete multiple items (book, folder, notes) and verify all propagate
3. **Manifest Verification**: Check that `manifest.deletions[]` contains deletion records after local deletions
4. **Edge Case Testing**: Delete items that don't exist in manifest, verify no errors
5. **Cleanup Verification**: Ensure pending deletions are cleared after successful recording

#### Physical File System Tests:
6. **Note File Deletion**: Delete a note in app, verify `.md` file is removed from sync directory
7. **Folder Deletion**: Delete a folder in app, verify entire folder tree is removed from sync directory
8. **Book Deletion**: Delete a book in app, verify book folder and all contents are removed from sync directory
9. **Empty Directory Cleanup**: Verify empty parent directories are cleaned up after deletions
10. **Sync Directory Integrity**: Ensure sync directory only contains files for items that exist in database

#### Error Handling Tests:
11. **Permission Errors**: Test deletion when sync directory files are read-only
12. **Missing Files**: Test deletion when physical files are already missing
13. **Partial Failures**: Verify manifest is still updated even if physical deletion fails
