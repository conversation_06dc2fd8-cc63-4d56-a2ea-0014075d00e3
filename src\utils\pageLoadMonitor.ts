/**
 * Page Load Performance Monitor
 * 
 * Tracks navigation timing data for sidebar navigation clicks
 * Provides clean data collection and export functionality
 */

import type { Router } from 'vue-router'

export interface PageLoadMetric {
  id: string
  timestamp: number
  fromRoute: string
  toRoute: string
  routeName: string
  navigationStartTime: number
  componentLoadTime: number
  mountTime: number
  totalLoadTime: number
  isFirstVisit: boolean
  userAgent: string
  memoryUsage?: number
}

export interface PerformanceReport {
  totalNavigations: number
  averageLoadTime: number
  fastestLoad: PageLoadMetric
  slowestLoad: PageLoadMetric
  routeAverages: Record<string, {
    averageTime: number
    visitCount: number
    firstVisitAverage: number
    subsequentVisitAverage: number
  }>
  metrics: PageLoadMetric[]
}

class PageLoadMonitor {
  private metrics: PageLoadMetric[] = []
  private currentNavigation: {
    id: string
    fromRoute: string
    toRoute: string
    routeName: string
    startTime: number
    componentLoadTime?: number
  } | null = null
  private visitedRoutes = new Set<string>()
  private isEnabled = true

  constructor() {
    // Load existing data from localStorage
    this.loadStoredMetrics()
  }

  /**
   * Initialize monitoring with Vue Router
   */
  public setupRouterMonitoring(router: Router): void {
    console.log('🔍 Setting up page load monitoring...')

    // Hook into router navigation
    router.beforeEach((to, from, next) => {
      if (!this.isEnabled) {
        next()
        return
      }

      this.startNavigation(from.path, to.path, to.name as string)
      next()
    })

    // Track when component loading completes
    router.afterEach((to, from) => {
      if (!this.isEnabled || !this.currentNavigation) return

      // Component has been resolved, mark component load time
      this.currentNavigation.componentLoadTime = performance.now()
    })
  }

  /**
   * Start tracking a navigation
   */
  private startNavigation(fromRoute: string, toRoute: string, routeName: string): void {
    const navigationId = `nav_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    this.currentNavigation = {
      id: navigationId,
      fromRoute,
      toRoute,
      routeName,
      startTime: performance.now()
    }

    console.log(`📊 Starting navigation tracking: ${fromRoute} → ${toRoute}`)
  }

  /**
   * Call this from component's onMounted hook to complete timing
   */
  public recordPageMounted(routeName: string): void {
    if (!this.isEnabled || !this.currentNavigation || this.currentNavigation.routeName !== routeName) {
      return
    }

    const mountTime = performance.now()
    const totalLoadTime = mountTime - this.currentNavigation.startTime
    const componentLoadTime = this.currentNavigation.componentLoadTime 
      ? this.currentNavigation.componentLoadTime - this.currentNavigation.startTime 
      : 0

    const isFirstVisit = !this.visitedRoutes.has(this.currentNavigation.toRoute)
    this.visitedRoutes.add(this.currentNavigation.toRoute)

    const metric: PageLoadMetric = {
      id: this.currentNavigation.id,
      timestamp: Date.now(),
      fromRoute: this.currentNavigation.fromRoute,
      toRoute: this.currentNavigation.toRoute,
      routeName: this.currentNavigation.routeName,
      navigationStartTime: this.currentNavigation.startTime,
      componentLoadTime,
      mountTime,
      totalLoadTime,
      isFirstVisit,
      userAgent: navigator.userAgent,
      memoryUsage: this.getMemoryUsage()
    }

    this.metrics.push(metric)
    this.saveMetricsToStorage()

    console.log(`✅ Page load recorded: ${routeName} (${totalLoadTime.toFixed(2)}ms)`, {
      componentLoad: `${componentLoadTime.toFixed(2)}ms`,
      totalTime: `${totalLoadTime.toFixed(2)}ms`,
      isFirstVisit,
      memoryUsage: metric.memoryUsage ? `${(metric.memoryUsage / 1024 / 1024).toFixed(2)}MB` : 'N/A'
    })

    // Clear current navigation
    this.currentNavigation = null
  }

  /**
   * Get memory usage if available
   */
  private getMemoryUsage(): number | undefined {
    if ('memory' in performance && (performance as any).memory) {
      return (performance as any).memory.usedJSHeapSize
    }
    return undefined
  }

  /**
   * Generate performance report
   */
  public generateReport(): PerformanceReport {
    if (this.metrics.length === 0) {
      return {
        totalNavigations: 0,
        averageLoadTime: 0,
        fastestLoad: {} as PageLoadMetric,
        slowestLoad: {} as PageLoadMetric,
        routeAverages: {},
        metrics: []
      }
    }

    const totalLoadTime = this.metrics.reduce((sum, m) => sum + m.totalLoadTime, 0)
    const averageLoadTime = totalLoadTime / this.metrics.length

    const sortedByTime = [...this.metrics].sort((a, b) => a.totalLoadTime - b.totalLoadTime)
    const fastestLoad = sortedByTime[0]
    const slowestLoad = sortedByTime[sortedByTime.length - 1]

    // Calculate route averages
    const routeGroups = this.metrics.reduce((groups, metric) => {
      const route = metric.routeName
      if (!groups[route]) {
        groups[route] = { all: [], first: [], subsequent: [] }
      }
      groups[route].all.push(metric.totalLoadTime)
      if (metric.isFirstVisit) {
        groups[route].first.push(metric.totalLoadTime)
      } else {
        groups[route].subsequent.push(metric.totalLoadTime)
      }
      return groups
    }, {} as Record<string, { all: number[], first: number[], subsequent: number[] }>)

    const routeAverages = Object.entries(routeGroups).reduce((averages, [route, times]) => {
      const avgAll = times.all.reduce((sum, time) => sum + time, 0) / times.all.length
      const avgFirst = times.first.length > 0 
        ? times.first.reduce((sum, time) => sum + time, 0) / times.first.length 
        : 0
      const avgSubsequent = times.subsequent.length > 0 
        ? times.subsequent.reduce((sum, time) => sum + time, 0) / times.subsequent.length 
        : 0

      averages[route] = {
        averageTime: avgAll,
        visitCount: times.all.length,
        firstVisitAverage: avgFirst,
        subsequentVisitAverage: avgSubsequent
      }
      return averages
    }, {} as Record<string, any>)

    return {
      totalNavigations: this.metrics.length,
      averageLoadTime,
      fastestLoad,
      slowestLoad,
      routeAverages,
      metrics: [...this.metrics]
    }
  }

  /**
   * Export data as CSV
   */
  public exportToCSV(): string {
    if (this.metrics.length === 0) return ''

    const headers = [
      'Timestamp',
      'From Route',
      'To Route', 
      'Route Name',
      'Component Load Time (ms)',
      'Total Load Time (ms)',
      'Is First Visit',
      'Memory Usage (MB)'
    ]

    const rows = this.metrics.map(metric => [
      new Date(metric.timestamp).toISOString(),
      metric.fromRoute,
      metric.toRoute,
      metric.routeName,
      metric.componentLoadTime.toFixed(2),
      metric.totalLoadTime.toFixed(2),
      metric.isFirstVisit ? 'Yes' : 'No',
      metric.memoryUsage ? (metric.memoryUsage / 1024 / 1024).toFixed(2) : 'N/A'
    ])

    return [headers, ...rows].map(row => row.join(',')).join('\n')
  }

  /**
   * Save metrics to localStorage
   */
  private saveMetricsToStorage(): void {
    try {
      localStorage.setItem('pageLoadMetrics', JSON.stringify(this.metrics))
    } catch (error) {
      console.warn('Failed to save metrics to localStorage:', error)
    }
  }

  /**
   * Load metrics from localStorage
   */
  private loadStoredMetrics(): void {
    try {
      const stored = localStorage.getItem('pageLoadMetrics')
      if (stored) {
        this.metrics = JSON.parse(stored)
        console.log(`📈 Loaded ${this.metrics.length} stored metrics`)
      }
    } catch (error) {
      console.warn('Failed to load stored metrics:', error)
      this.metrics = []
    }
  }

  /**
   * Clear all collected data
   */
  public clearData(): void {
    this.metrics = []
    this.visitedRoutes.clear()
    localStorage.removeItem('pageLoadMetrics')
    console.log('🗑️ Page load metrics cleared')
  }

  /**
   * Enable/disable monitoring
   */
  public setEnabled(enabled: boolean): void {
    this.isEnabled = enabled
    console.log(`📊 Page load monitoring ${enabled ? 'enabled' : 'disabled'}`)
  }

  /**
   * Get current metrics count
   */
  public getMetricsCount(): number {
    return this.metrics.length
  }
}

// Export singleton instance
export const pageLoadMonitor = new PageLoadMonitor()
