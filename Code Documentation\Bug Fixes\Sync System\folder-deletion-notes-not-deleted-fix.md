# Folder Deletion Bug Fix - Notes Not Being Deleted

## Issue Description

When users delete a folder and choose "Delete all notes" option, the notes inside the folder were not being deleted from the database. Instead, they were being orphaned (folder_id set to NULL) and then re-imported by the sync system, causing the deleted folder to reappear.

## Root Cause Analysis

### The Problem Flow
1. User deletes folder in UI and selects "None (Delete all notes)" option
2. <PERSON><PERSON> calls `db.folders.delete(folder.id, null)` with `targetFolderId = null`
3. Backend `deleteFolderAndHandleNotes()` function receives `targetFolderId = null`
4. **BUG**: Function only handled `targetFolderId !== null` case (moving notes)
5. When `targetFolderId = null`, no action was taken on notes
6. Folder deletion proceeded with database constraint `ON DELETE SET NULL`
7. Notes had their `folder_id` set to `NULL` instead of being deleted
8. Sync system detected orphaned notes and re-created folder structure

### Database Schema Context
```sql
-- Notes table foreign key constraint
FOREIGN KEY (folder_id) REFERENCES folders(id) ON DELETE SET NULL
```

This constraint was designed to preserve notes when folders are accidentally deleted, but the UI provides explicit user choice to delete notes.

## Files Modified

- `electron/main/api/folders-api.ts` - Fixed `deleteFolderAndHandleNotes()` function

## Solution Implemented

### Before (Buggy Code)
```typescript
if (targetFolderId !== null) {
    // Move notes to target folder
    const notesToMove: Note[] = await getNotesByFolderId(id);
    const updatePromises = notesToMove.map(note =>
        updateNote(note.id!, { folder_id: targetFolderId })
    );
    await Promise.all(updatePromises);
}
// Notes were ignored when targetFolderId === null
```

### After (Fixed Code)
```typescript
// Get all notes in the folder before processing
const notesInFolder: Note[] = await getNotesByFolderId(id);

if (targetFolderId !== null) {
    // Move notes to target folder
    const targetFolderExists = await getFolderById(targetFolderId);
    if (!targetFolderExists) {
        throw new Error(`Target folder with ID ${targetFolderId} not found`);
    }

    const updatePromises = notesInFolder.map(note =>
        updateNote(note.id!, { folder_id: targetFolderId })
    );
    await Promise.all(updatePromises);
} else {
    // Delete all notes in the folder (user chose "Delete all notes")
    const { deleteNoteWithValidation } = await import('./notes-api');
    
    for (const note of notesInFolder) {
        if (note.id !== undefined) {
            await deleteNoteWithValidation(note.id);
        }
    }
}
```

## Key Changes

1. **Added explicit note deletion**: When `targetFolderId` is `null`, iterate through all notes in the folder and delete them individually
2. **Used proper deletion function**: Called `deleteNoteWithValidation()` to ensure database hooks are triggered for sync system
3. **Maintained existing behavior**: Moving notes to target folder still works as before
4. **Proper error handling**: Maintained existing error handling patterns

## Technical Details

### Why `deleteNoteWithValidation()`?
- Triggers database hooks for sync system notification
- Ensures proper deletion tracking in sync manifest
- Handles all note deletion side effects (media files, etc.)

### Import Strategy
Used dynamic import to avoid circular dependency issues between `folders-api.ts` and `notes-api.ts`.

## Expected Behavior After Fix

1. User deletes folder and chooses "None (Delete all notes)"
2. All notes in folder are properly deleted from database
3. Database hooks trigger note deletion events for sync system
4. Sync system records note deletions in manifest
5. Folder deletion proceeds normally
6. No orphaned notes remain to cause folder re-creation

## Testing Verification

To verify the fix:
1. Create a folder with notes
2. Delete the folder and select "None (Delete all notes)"
3. Check that notes are deleted from database (not just orphaned)
4. Verify sync system doesn't re-create the folder
5. Confirm manifest properly records all deletions

## Related Issues

This fix addresses the sync system issue where deleted folders would reappear due to orphaned notes being re-synced.
