/* Local Google Fonts for Noti Application */
/* This file contains @font-face declarations for locally hosted Google Fonts */

/* Montserrat Font Family */
@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url('./fonts/montserrat/montserrat-300.ttf') format('truetype');
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('./fonts/montserrat/montserrat-400.ttf') format('truetype');
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url('./fonts/montserrat/montserrat-500.ttf') format('truetype');
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url('./fonts/montserrat/montserrat-600.ttf') format('truetype');
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url('./fonts/montserrat/montserrat-700.ttf') format('truetype');
}

/* Roboto Font Family */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url('./fonts/roboto/roboto-300.ttf') format('truetype');
}

@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('./fonts/roboto/roboto-400.ttf') format('truetype');
}

@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url('./fonts/roboto/roboto-500.ttf') format('truetype');
}

@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url('./fonts/roboto/roboto-700.ttf') format('truetype');
}

/* Open Sans Font Family */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url('./fonts/opensans/opensans-300.ttf') format('truetype');
}

@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('./fonts/opensans/opensans-400.ttf') format('truetype');
}

@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url('./fonts/opensans/opensans-500.ttf') format('truetype');
}

@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url('./fonts/opensans/opensans-600.ttf') format('truetype');
}

@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url('./fonts/opensans/opensans-700.ttf') format('truetype');
}

/* Lato Font Family */
@font-face {
  font-family: 'Lato';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url('./fonts/lato/lato-300.ttf') format('truetype');
}

@font-face {
  font-family: 'Lato';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('./fonts/lato/lato-400.ttf') format('truetype');
}

@font-face {
  font-family: 'Lato';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url('./fonts/lato/lato-700.ttf') format('truetype');
}

/* CSS Custom Properties for Font Families */
:root {
  --montserrat: 'Montserrat', sans-serif;
  --roboto: 'Roboto', sans-serif;
  --open-sans: 'Open Sans', sans-serif;
  --lato: 'Lato', sans-serif;
  --arial: Arial, sans-serif;
  --times: 'Times New Roman', serif;
  --georgia: Georgia, serif;
  --courier: 'Courier New', monospace;
  --verdana: Verdana, sans-serif;
  --helvetica: Helvetica, sans-serif;
}

/* Custom classes for applying fonts */
.font-montserrat {
  font-family: 'Montserrat', sans-serif !important;
}

.font-roboto {
  font-family: 'Roboto', sans-serif !important;
}

.font-open-sans {
  font-family: 'Open Sans', sans-serif !important;
}

.font-lato {
  font-family: 'Lato', sans-serif !important;
}

.font-arial {
  font-family: Arial, sans-serif !important;
}

.font-times-new-roman {
  font-family: 'Times New Roman', serif !important;
}

.font-georgia {
  font-family: Georgia, serif !important;
}

.font-courier-new {
  font-family: 'Courier New', monospace !important;
}

.font-verdana {
  font-family: Verdana, sans-serif !important;
}

.font-helvetica {
  font-family: Helvetica, sans-serif !important;
}

/* Direct attribute selectors for TipTap font styles */
[style*="font-family: Montserrat"] {
  font-family: 'Montserrat', sans-serif !important;
}

[style*="font-family: Arial"] {
  font-family: Arial, sans-serif !important;
}

[style*="font-family: Times New Roman"] {
  font-family: 'Times New Roman', serif !important;
}

[style*="font-family: Georgia"] {
  font-family: Georgia, serif !important;
}

[style*="font-family: Courier New"] {
  font-family: 'Courier New', monospace !important;
}

[style*="font-family: Verdana"] {
  font-family: Verdana, sans-serif !important;
}

[style*="font-family: Helvetica"] {
  font-family: Helvetica, sans-serif !important;
}

[style*="font-family: Roboto"] {
  font-family: 'Roboto', sans-serif !important;
}

[style*="font-family: Open Sans"] {
  font-family: 'Open Sans', sans-serif !important;
}

[style*="font-family: Lato"] {
  font-family: 'Lato', sans-serif !important;
}
