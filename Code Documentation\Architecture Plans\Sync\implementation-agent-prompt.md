# Implementation Agent Prompt: Sync System .noti Format Migration

## Task Overview

You are tasked with implementing a migration of the Noti app's sync system from using Markdown (.md) files to the enhanced .noti format for note storage. This is a focused implementation task with specific requirements and constraints.

## Context and Background

The Noti app currently has:
1. **Sync System**: Located in `/electron/main/api/sync-logic/` that syncs notes as `.md` files
2. **Export System**: Already implemented .noti format for export/import in `notes-api.ts`
3. **Media System**: Existing media handling with `media_files` table and `noti-media://` URLs

## Specific Requirements

### .noti Format for Sync (Simplified)
```json
{
  "version": "1.0",
  "type": "noti-note", 
  "schema": "https://noti.app/schemas/note/v1.0",
  "metadata": {
    "id": 123,
    "title": "Note Title",
    "created_at": "2024-01-15T10:30:00.000Z",
    "updated_at": "2024-01-15T14:30:00.000Z",
    "last_viewed_at": "2024-01-15T14:30:00.000Z",
    "type": "text",
    "color": "#ff0000",
    "folder_id": 456,
    "book_id": 789
  },
  "content": {
    "html": "<p>Full rich HTML content</p><img src=\"noti-media://embedded_media_1\">",
    "markdown": "First 50 words for preview...",
    "plain_text": "Plain text for search"
  },
  "media": [
    {
      "id": "embedded_media_1",
      "file_name": "image.png",
      "file_type": "image/png", 
      "file_size": 102400,
      "original_path": "noti-media://original/path",
      "embedded": true,
      "data": "base64_encoded_image_data"
    }
  ]
}
```

### Key Constraints
1. **HTML Priority**: Store full HTML content, markdown is just first ~50 words for preview
2. **Media Embedding**: All media files must be base64-embedded in .noti files
3. **No Migration**: App hasn't been released, so no backward compatibility needed
4. **Manifest Unchanged**: Keep existing manifest structure, just change file paths from .md to .noti

## Files to Modify

### Primary Files (Must Change)
1. **`electron/main/api/sync-logic/file-operations.ts`**
   - Update `readNote()` and `writeNote()` methods for .noti format
   - Change from reading/writing .md files to .noti JSON files

2. **`electron/main/api/sync-logic/unified-sync-engine.ts`**
   - Update `exportNote()` method to create .noti files with embedded media
   - Update `importNote()` method to parse .noti files and restore media
   - Change file paths from `.md` to `.noti`

3. **`electron/main/api/sync-logic/manifest-manager.ts`**
   - Update path generation: change `.md` extensions to `.noti`
   - Lines ~402 and ~413 where note paths are built

4. **`electron/main/api/sync-logic/types.ts`**
   - Add .noti format type definitions

### New Files to Create
1. **`electron/main/api/sync-logic/media-utils.ts`**
   - Media embedding and extraction utilities

## Implementation Steps

### Step 1: Add Type Definitions
Add to `types.ts`:
```typescript
export interface NotiFileData {
  version: string;
  type: string;
  schema: string;
  metadata: NotiMetadata;
  content: NotiContent;
  media: EmbeddedMedia[];
}

export interface NotiMetadata {
  id: number;
  title: string;
  created_at: string;
  updated_at: string;
  last_viewed_at?: string;
  type: string;
  color?: string;
  folder_id?: number;
  book_id?: number;
}

export interface NotiContent {
  html: string;
  markdown: string;
  plain_text: string;
}

export interface EmbeddedMedia {
  id: string;
  file_name: string;
  file_type: string;
  file_size: number;
  original_path: string;
  embedded: boolean;
  data: string;
}
```

### Step 2: Create Media Utilities
Create `media-utils.ts` with functions:
- `embedMediaFiles(noteId: number): Promise<EmbeddedMedia[]>`
- `restoreEmbeddedMedia(htmlContent: string, media: EmbeddedMedia[], noteId: number): Promise<string>`
- `createMarkdownPreview(content: string): string`
- `replaceMediaUrlsWithEmbedded(htmlContent: string, mediaFiles: any[], embeddedMedia: EmbeddedMedia[]): string`

### Step 3: Update File Operations
Modify `file-operations.ts`:
- Change `readNote()` to parse JSON instead of reading plain text
- Change `writeNote()` to write JSON instead of plain text
- Update file extension handling

### Step 4: Update Sync Engine
Modify `unified-sync-engine.ts`:
- In `exportNote()`: Get media files, embed as base64, create .noti structure
- In `importNote()`: Parse .noti, restore media to database, update HTML URLs
- Change all file paths from `.md` to `.noti`

### Step 5: Update Manifest Manager
Modify `manifest-manager.ts`:
- Change file extension in path generation from `.md` to `.noti`
- Update hash calculation if needed

## Media Workflow Details

### Export Process
1. Get note from database with `html_content`
2. Query `media_files` table for associated media
3. Read media files from disk and convert to base64
4. Replace `noti-media://` URLs in HTML with embedded references
5. Create markdown preview (first 50 words)
6. Save as .noti JSON file

### Import Process  
1. Parse .noti JSON file
2. Create note in database with preview markdown
3. For each embedded media: decode base64, save to media storage, insert into `media_files` table
4. Replace embedded references in HTML with new `noti-media://` URLs
5. Update note with restored HTML content

## Testing Requirements

Create tests to verify:
1. .noti file creation with embedded media
2. .noti file parsing and media restoration
3. Round-trip integrity (export then import)
4. Sync operations with .noti format
5. Media URL replacement accuracy

## Success Criteria

- [ ] Sync system creates .noti files instead of .md files
- [ ] Media files are properly embedded as base64 in .noti files
- [ ] Import process correctly restores media to `media_files` table
- [ ] HTML content URLs are properly updated during import/export
- [ ] Existing sync functionality works with new format
- [ ] No data loss during sync operations

## Important Notes

1. **Use Existing Media API**: Import and use functions from `../media-api.ts`
2. **Preserve Relationships**: Maintain folder/book relationships in manifest
3. **Error Handling**: Add proper error handling for media operations
4. **Performance**: Be mindful of memory usage with large embedded media
5. **File Extensions**: Consistently use `.noti` instead of `.md` throughout

## Reference Implementation

Look at the existing .noti export/import implementation in `notes-api.ts` (lines 907-1580) for reference on:
- .noti file structure
- Media embedding process
- Base64 encoding/decoding
- Media restoration workflow

## Expected Outcome

After implementation, the sync system should:
- Create portable .noti files with embedded media
- Maintain all existing sync functionality
- Preserve rich HTML content during sync
- Provide fast preview text for notes list
- Seamlessly integrate with existing media system

Start with the type definitions and media utilities, then work through the file operations and sync engine updates. Test thoroughly with notes containing images to ensure media embedding and restoration works correctly.
