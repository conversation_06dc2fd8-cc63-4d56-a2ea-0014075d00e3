# .noti Custom File Format - Technical Assessment

## Executive Summary

This document provides a comprehensive technical assessment for implementing a custom `.noti` file format for the Noti application. The assessment covers technical feasibility, implementation requirements, web standards compliance, and integration with existing systems.

## Current State Analysis

### Existing Export System
The application currently supports three export formats:
- **PDF**: Full note content with formatting via Puppeteer
- **Markdown (.md)**: Plain text markdown format  
- **Noti (.noti.json)**: Native format preserving all metadata (already partially implemented)

**Key Finding**: A `.noti.json` format already exists in the codebase but uses `.noti.json` extension instead of `.noti`.

### Current Import System
The application supports importing:
- **Markdown (.md)**: Converted to HTML for editor
- **PDF**: Text extraction (limited)
- **Noti (.noti.json)**: JSON parsing with metadata preservation
- **Text (.txt)**: Plain text import

### Sync System Architecture
The sync system currently uses:
- **File Format**: Markdown (.md) files for note content
- **Metadata Storage**: Manifest-based tracking with `.sync-manifest.json`
- **Structure**: Hierarchical organization (Books/Folders/Notes)
- **Change Detection**: Hash-based comparison for synchronization

## Technical Feasibility Assessment

### ✅ High Feasibility Areas

1. **Export System Enhancement**
   - Existing `.noti.json` export can be easily modified to use `.noti` extension
   - JSON structure already preserves HTML content and metadata
   - File generation logic is already implemented

2. **Import System Enhancement**
   - JSON parsing infrastructure exists
   - Metadata handling is already implemented
   - File validation and error handling patterns established

3. **File Format Definition**
   - JSON-based format aligns with existing patterns
   - HTML content preservation maintains editor compatibility
   - Metadata structure matches database schema

### ⚠️ Medium Complexity Areas

1. **Sync System Integration**
   - Requires modification of file operations in `sync-logic/` folder
   - Manifest format needs updating for new file extension
   - Change detection algorithms need adjustment

2. **MIME Type Registration**
   - Custom MIME type definition required
   - Operating system file association setup
   - Electron file dialog filter updates

### 🔴 High Complexity Areas

1. **Backward Compatibility**
   - Existing sync directories use `.md` format
   - Migration strategy needed for current users
   - Dual-format support during transition period

## Proposed File Format Specification

### File Extension
- **Primary**: `.noti`
- **MIME Type**: `application/vnd.noti+json` (vendor tree registration)

### JSON Structure
```json
{
  "version": "1.0",
  "type": "noti-note",
  "metadata": {
    "id": 123,
    "title": "Note Title",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T14:30:00Z",
    "type": "text",
    "color": "#ff0000",
    "folder_id": 456,
    "book_id": 789,
    "last_viewed_at": "2024-01-15T14:30:00Z"
  },
  "content": {
    "html": "<p>Rich HTML content from editor</p>",
    "markdown": "Plain markdown content (optional)",
    "word_count": 150
  }
}
```

### Design Rationale
- **HTML-First**: Preserves exact editor formatting
- **Metadata Complete**: All database fields included
- **Version Support**: Future format evolution capability
- **Self-Contained**: No external dependencies

## Implementation Plan

### Phase 1: Export System (Low Risk)
**Files to Modify:**
- `electron/main/api/notes-api.ts` (lines 820-842, 860-890)
- `src/components/modals/ExportNoteModal.vue`

**Changes Required:**
1. Modify `exportNoteToNoti()` to use `.noti` extension
2. Update file dialog filters
3. Enhance JSON structure with HTML content
4. Update export format validation

**Estimated Effort**: 4-6 hours

### Phase 2: Import System (Low Risk)  
**Files to Modify:**
- `electron/main/api/notes-api.ts` (lines 1142-1219)
- `electron/main/ipc-handlers.ts` (line 190)

**Changes Required:**
1. Update format validation to accept `.noti`
2. Enhance JSON parsing for new structure
3. HTML content handling improvements
4. Error handling for malformed files

**Estimated Effort**: 6-8 hours

### Phase 3: MIME Type & File Associations (Medium Risk)
**Files to Modify:**
- `electron/main/main.ts` (app initialization)
- `package.json` (file associations)

**Changes Required:**
1. Register custom MIME type with OS
2. Set up file associations in Electron
3. Update file dialog configurations
4. Icon association setup

**Estimated Effort**: 8-12 hours

### Phase 4: Sync System Integration (High Risk)
**Files to Modify:**
- `electron/main/api/sync-logic/unified-sync-engine.ts`
- `electron/main/api/sync-logic/file-operations.ts`
- `electron/main/api/sync-logic/manifest-manager.ts`
- `electron/main/api/sync-logic/change-detector.ts`
- `electron/main/api/sync-logic/import-handler.ts`

**Changes Required:**
1. Replace `.md` file operations with `.noti`
2. Update manifest structure for new format
3. Modify change detection algorithms
4. Update import/export logic in sync engine
5. File path generation updates

**Estimated Effort**: 20-30 hours

## Risk Analysis

### Technical Risks

1. **Sync System Complexity** (High)
   - The sync system is tightly coupled to Markdown format
   - Multiple components need coordinated changes
   - Risk of breaking existing sync functionality

2. **File Size Impact** (Medium)
   - HTML content will increase file sizes significantly
   - May impact sync performance and storage requirements
   - Network transfer implications for cloud sync

3. **Backward Compatibility** (High)
   - Existing users have `.md` files in sync directories
   - Need migration strategy without data loss
   - Dual-format support complexity

### Mitigation Strategies

1. **Phased Implementation**
   - Start with export/import (low risk)
   - Thoroughly test before sync integration
   - Feature flags for gradual rollout

2. **Backward Compatibility**
   - Maintain `.md` import capability
   - Provide migration tools
   - Clear user communication about changes

3. **Performance Optimization**
   - Compress JSON content
   - Lazy loading for large files
   - Chunked sync for large datasets

## Web Standards Compliance

### MIME Type Registration (RFC 6838)

**Recommended MIME Type**: `application/vnd.noti+json`

**Registration Details**:
- **Tree**: Vendor tree (`vnd.`) - appropriate for proprietary application formats
- **Structured Suffix**: `+json` - indicates JSON-based content structure
- **Top-level Type**: `application` - for data processed by applications
- **Registration Status**: Not required for private/internal use

**MIME Type Registration Template** (if public registration desired):
```
Type name: application
Subtype name: vnd.noti+json
Required parameters: None
Optional parameters: charset (default: UTF-8)
Encoding considerations: 8bit or binary
Security considerations:
  - May contain HTML content requiring sanitization
  - JSON parsing vulnerabilities possible
  - No active content execution
Interoperability considerations:
  - Requires Noti application for proper interpretation
  - JSON structure allows partial compatibility with generic JSON parsers
Published specification: Internal Noti application documentation
Applications that use this media type: Noti note-taking application
Fragment identifier considerations: Not applicable
Additional information:
  Magic number(s): None (JSON text format)
  File extension(s): .noti
  Macintosh file type code(s): Not assigned
Person & email address to contact: [Application maintainer]
Intended usage: LIMITED USE
Restrictions on usage: Designed specifically for Noti application
Author: Noti Development Team
Change controller: Noti Development Team
```

### Operating System File Associations

**Windows Implementation**:
```javascript
// In electron/main/main.ts
app.setAsDefaultProtocolClient('noti');

// Registry entries (handled by Electron installer)
// HKEY_CLASSES_ROOT\.noti
//   (Default) = "NotiFile"
// HKEY_CLASSES_ROOT\NotiFile
//   (Default) = "Noti Note File"
// HKEY_CLASSES_ROOT\NotiFile\shell\open\command
//   (Default) = "path\to\noti.exe" "%1"
```

**macOS Implementation**:
```xml
<!-- In package.json build configuration -->
"mac": {
  "fileAssociations": [
    {
      "ext": "noti",
      "name": "Noti Note File",
      "description": "Noti application note file",
      "mimeType": "application/vnd.noti+json",
      "role": "Editor"
    }
  ]
}
```

**Linux Implementation**:
```xml
<!-- Desktop entry file -->
<mime-info xmlns="http://www.freedesktop.org/standards/shared-mime-info">
  <mime-type type="application/vnd.noti+json">
    <comment>Noti Note File</comment>
    <glob pattern="*.noti"/>
  </mime-type>
</mime-info>
```

### Electron File Dialog Configuration

```typescript
// Update file filters in dialog configurations
const filters = [
  { name: 'Noti Files', extensions: ['noti'] },
  { name: 'Markdown Files', extensions: ['md'] },
  { name: 'All Files', extensions: ['*'] }
];
```

### Security Considerations

**JSON Parsing Security**:
- Validate JSON structure before processing
- Limit file size to prevent memory exhaustion
- Sanitize HTML content to prevent XSS

**File System Security**:
- Validate file extensions
- Restrict file access to application directories
- Implement proper error handling for malformed files

**Content Security**:
- HTML content requires sanitization before display
- No executable content allowed in format
- Metadata validation to prevent injection attacks

## Alternative Approaches Considered

### 1. Keep Current `.noti.json` Format
**Pros**: No breaking changes, immediate availability
**Cons**: Not truly "custom" format, confusing extension

### 2. Binary Format
**Pros**: Smaller file sizes, harder to tamper with
**Cons**: Complex implementation, debugging difficulties

### 3. XML-Based Format
**Pros**: Structured, widely supported
**Cons**: Verbose, larger file sizes than JSON

## Recommendations

### Immediate Actions (Recommended)
1. **Implement Export Enhancement** (Phase 1)
   - Low risk, immediate user value
   - Foundation for future phases
   - Easy to test and validate

2. **Implement Import Enhancement** (Phase 2)
   - Completes basic file format support
   - Enables user workflows
   - Prepares for sync integration

### Future Considerations (Evaluate Later)
1. **Sync System Integration** (Phase 4)
   - High complexity and risk
   - Requires extensive testing
   - Consider user demand and feedback first

2. **MIME Type Registration** (Phase 3)
   - Medium priority
   - Can be implemented independently
   - Enhances user experience

## Detailed Implementation Specifications

### Export System Code Changes

**File**: `electron/main/api/notes-api.ts`

```typescript
// Current function (lines 820-842)
const exportNoteToNoti = async (note: Note): Promise<string> => {
    const exportDir = getExportDirectory();
    const sanitizedTitle = sanitizeFilename(note.title);
    const outputPath = path.join(exportDir, `${sanitizedTitle}.noti`); // Changed extension

    const notiData = {
        version: "1.0",
        type: "noti-note",
        metadata: {
            id: note.id,
            title: note.title,
            created_at: note.created_at,
            updated_at: note.updated_at,
            type: note.type || 'text',
            color: note.color,
            folder_id: note.folder_id,
            book_id: note.book_id,
            last_viewed_at: note.last_viewed_at
        },
        content: {
            html: note.html_content || '',
            markdown: note.content || '',
            word_count: (note.content || '').split(/\s+/).length
        }
    };

    try {
        fs.writeFileSync(outputPath, JSON.stringify(notiData, null, 2));
        return outputPath;
    } catch (error: any) {
        console.error('Error exporting to Noti format:', error);
        throw new Error(`Failed to export note as Noti format: ${error.message}`);
    }
};
```

**File Dialog Updates** (line 860):
```typescript
// Change file extension filter
const extension = format === 'noti' ? 'noti' : format; // Remove .json suffix
```

### Import System Code Changes

**File**: `electron/main/api/notes-api.ts`

```typescript
// Enhanced import function (lines 1172-1188)
if (format.toLowerCase() === 'noti') {
    try {
        const notiData = JSON.parse(content);

        // Validate .noti file structure
        if (!notiData.version || !notiData.type || notiData.type !== 'noti-note') {
            throw new Error('Invalid .noti file format');
        }

        // Extract content - prefer HTML, fallback to markdown
        if (notiData.content?.html) {
            htmlContent = notiData.content.html;
            noteContent = notiData.content.markdown || '';
        } else if (notiData.content?.markdown) {
            noteContent = notiData.content.markdown;
            htmlContent = md.render(noteContent);
        } else {
            // Legacy support for old .noti.json format
            noteContent = notiData.content || '';
            htmlContent = notiData.html_content || `<p>${escapeHtml(noteContent)}</p>`;
        }

        // Use metadata title if available
        if (notiData.metadata?.title && title === 'Imported Note') {
            title = notiData.metadata.title;
        }
    } catch (parseError) {
        console.warn('Failed to parse .noti file:', parseError);
        throw new Error('Invalid .noti file format');
    }
}
```

### Sync System Integration Specifications

**Critical Files Requiring Changes:**

1. **unified-sync-engine.ts** (lines 1217-1263)
   - Change file extension from `.md` to `.noti`
   - Update content structure for JSON format
   - Modify metadata handling

2. **file-operations.ts** (lines 109-135)
   - Update `writeNote()` method for JSON format
   - Change file extension handling
   - Modify atomic write operations

3. **manifest-manager.ts** (lines 433-449)
   - Update manifest item creation for `.noti` files
   - Modify hash calculation for JSON content
   - Update path generation logic

4. **import-handler.ts** (lines 214-228, 436-447)
   - Change file extension detection from `.md` to `.noti`
   - Update metadata extraction logic
   - Modify content parsing for JSON format

### Database Schema Considerations

**No schema changes required** - the existing Note interface already supports all necessary fields:

```typescript
interface Note {
  id?: number;
  title: string;
  content?: string;        // Markdown content
  html_content?: string;   // HTML content
  folder_id?: number | null;
  book_id?: number | null;
  type?: string;
  color?: string | null;
  // ... other fields
}
```

### Performance Impact Analysis

**File Size Comparison** (estimated):
- Current `.md` file: ~1KB per note
- Proposed `.noti` file: ~3-5KB per note (due to HTML content)
- Impact: 3-5x increase in sync directory size

**Sync Performance**:
- Hash calculation: Minimal impact (JSON is still text)
- Network transfer: 3-5x more data to transfer
- Storage: Proportional increase in disk usage

### Migration Strategy

**Option 1: Gradual Migration**
1. Support both `.md` and `.noti` formats in sync system
2. Export new notes as `.noti`, keep existing as `.md`
3. Provide manual migration tool for users

**Option 2: One-time Migration**
1. Convert all existing `.md` files to `.noti` format
2. Update manifest to reflect new file paths
3. Backup original files before conversion

**Option 3: Dual Format Support**
1. Maintain `.md` for sync system (lightweight)
2. Use `.noti` only for export/import operations
3. Convert between formats as needed

## Conclusion

The `.noti` custom file format is **technically feasible** with varying levels of complexity across different implementation phases. The export and import systems can be enhanced relatively easily, while sync system integration presents significant challenges.

**Recommended Approach**: Implement in phases, starting with export/import functionality to validate user demand and technical approach before tackling the more complex sync system integration.

**Total Estimated Effort**: 38-56 hours across all phases
**Risk Level**: Medium to High (depending on scope)
**User Impact**: High positive (native file format support)
